# <PERSON> - Personal Website

A modern, interactive personal website showcasing the intersection of code, cuisine, and creativity. Built with React, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Interactive Particle Trail**: Dynamic particle effects that follow mouse movement
- **Responsive Design**: Optimized for all device sizes
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Component-Based Architecture**: Modular React components for easy maintenance
- **TypeScript**: Full type safety and better developer experience
- **Tailwind CSS**: Utility-first CSS framework for rapid styling

## 🛠️ Tech Stack

- **Frontend Framework**: React 18
- **Language**: TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Linting**: ESLint with TypeScript support

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd paulvz-website
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## 🏗️ Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the project for production
- `npm run preview` - Preview the production build locally
- `npm run lint` - Run ESLint to check for code issues

## 📁 Project Structure

```
src/
├── components/
│   ├── Contact.tsx      # Contact section component
│   ├── Gallery.tsx      # Image gallery component
│   ├── Hero.tsx         # Hero/landing section
│   ├── ParticleTrail.tsx # Interactive particle effects
│   ├── Pillars.tsx      # Core pillars/values section
│   └── Story.tsx        # About/story section
├── App.tsx              # Main application component
├── main.tsx            # Application entry point
├── index.css           # Global styles
└── vite-env.d.ts       # Vite type definitions
```

## 🎨 Customization

The website is built with modularity in mind. Each section is a separate component that can be easily modified:

- **Hero Section**: Update the main headline and background
- **Story Section**: Modify the personal story and background
- **Pillars Section**: Customize the core values/pillars
- **Gallery Section**: Add or update portfolio images
- **Contact Section**: Update contact information and social links

## 🚀 Deployment

1. Build the project:
```bash
npm run build
```

2. The `dist` folder will contain the production-ready files that can be deployed to any static hosting service like:
   - Vercel
   - Netlify
   - GitHub Pages
   - AWS S3
   - Any web server

## 🤝 Contributing

This is a personal website, but if you find any bugs or have suggestions for improvements, feel free to open an issue or submit a pull request.

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 📞 Contact

For any questions or inquiries, please reach out through the contact section on the website.

---

Built with ❤️ using React and TypeScript
