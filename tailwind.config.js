/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        ruby: {
          50: 'oklch(96.83% 0.014 17.52)',
          100: 'oklch(92.53% 0.033 17.82)',
          200: 'oklch(86.19% 0.066 18.53)',
          300: 'oklch(78.85% 0.107 19.74)',
          400: 'oklch(71.75% 0.156 21.77)',
          500: 'oklch(65.45% 0.207 24.93)',
          600: 'oklch(57.71% 0.215 27.33)',
          700: 'oklch(46.98% 0.175 27.35)',
          800: 'oklch(36.07% 0.134 27.31)',
          900: 'oklch(25.15% 0.094 27.42)',
          950: 'oklch(19.78% 0.073 27.04)',
        },
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'bounce-gentle': 'bounceGentle 2s ease-in-out infinite',
        'float': 'float 3s ease-in-out infinite',
        'shimmer': 'shimmer 2s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
      },
    },
  },
  plugins: [],
};