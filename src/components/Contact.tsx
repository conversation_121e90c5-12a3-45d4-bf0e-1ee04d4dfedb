import React, { useState, useEffect, useRef } from 'react';
import { Instagram, Mail, Code, ChefHat, Gamepad2 } from 'lucide-react';

const Contact: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredIcon, setHoveredIcon] = useState<string | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section id="contact" ref={sectionRef} className="py-20 bg-gradient-to-br from-ruby-900 to-ruby-800">
      <div className="max-w-4xl mx-auto px-6 text-center">
        {/* Fun Combined Icon */}
        <div className={`mb-8 transition-all duration-800 ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
          <div className="relative inline-block">
            <div className="flex items-center justify-center space-x-4 p-6 bg-white/10 backdrop-blur-sm rounded-2xl">
              <Code className="w-8 h-8 text-blue-300 animate-float" />
              <div className="text-white text-2xl">×</div>
              <ChefHat className="w-8 h-8 text-orange-300 animate-float" style={{ animationDelay: '0.5s' }} />
              <div className="text-white text-2xl">×</div>
              <Gamepad2 className="w-8 h-8 text-green-300 animate-float" style={{ animationDelay: '1s' }} />
            </div>
          </div>
        </div>

        {/* Heading */}
        <div className={`mb-8 transition-all duration-800 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Like what you see?
          </h2>
          <p className="text-xl text-ruby-200 mb-8">
            Whether you want to talk tech, swap recipes, or challenge me to a game.
          </p>
        </div>

        {/* Social Links */}
        <div className={`transition-all duration-800 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <p className="text-ruby-200 mb-8 text-lg">
            The best place to see what I'm up to right now is on my Instagram:
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            {/* Instagram */}
            <a
              href="https://instagram.com/paulvz"
              target="_blank"
              rel="noopener noreferrer"
              className="group flex items-center gap-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-8 py-4 text-white hover:bg-white/20 transition-all duration-300 hover:scale-105"
              onMouseEnter={() => setHoveredIcon('instagram')}
              onMouseLeave={() => setHoveredIcon(null)}
            >
              <Instagram className={`w-6 h-6 transition-transform duration-300 ${hoveredIcon === 'instagram' ? 'animate-bounce-gentle' : ''}`} />
              <span className="font-medium text-lg">@paulvz</span>
            </a>

            {/* Email */}
            <a
              href="mailto:<EMAIL>"
              className="group flex items-center gap-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-8 py-4 text-white hover:bg-white/20 transition-all duration-300 hover:scale-105"
              onMouseEnter={() => setHoveredIcon('email')}
              onMouseLeave={() => setHoveredIcon(null)}
            >
              <Mail className={`w-6 h-6 transition-transform duration-300 ${hoveredIcon === 'email' ? 'animate-bounce-gentle' : ''}`} />
              <span className="font-medium">Say Hello</span>
              {hoveredIcon === 'email' && (
                <div className="absolute -top-2 -right-2 text-2xl animate-bounce">
                  ✈️
                </div>
              )}
            </a>
          </div>
        </div>

        {/* Footer */}
        <div className={`mt-16 pt-8 border-t border-white/20 transition-all duration-800 delay-700 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
          <p className="text-ruby-300 text-sm">
            © 2025 Paul. Built with React, TypeScript, and a lot of ☕
          </p>
        </div>
      </div>
    </section>
  );
};

export default Contact;