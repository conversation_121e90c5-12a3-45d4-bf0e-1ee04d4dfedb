import React, { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

const Hero: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 500);
    return () => clearTimeout(timer);
  }, []);

  const scrollToNext = () => {
    const nextSection = document.getElementById('story');
    nextSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="relative h-screen overflow-hidden bg-gradient-to-br from-ruby-900 via-ruby-800 to-ruby-700">
      {/* Video Background Placeholder */}
      <div className="absolute inset-0 bg-gradient-to-br from-ruby-900/80 via-ruby-800/70 to-ruby-700/80">
        <div className="absolute inset-0 bg-[url('https://images.pexels.com/photos/574071/pexels-photo-574071.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')] bg-cover bg-center opacity-30" />
      </div>

      {/* Content */}
      <div className="relative z-20 flex items-center justify-center h-full text-center text-white">
        <div className="max-w-4xl mx-auto px-6">
          <h1 className={`text-6xl md:text-8xl font-bold mb-6 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <span className="bg-gradient-to-r from-white to-ruby-200 bg-clip-text text-transparent">
              Code, Cuisine & Creativity
            </span>
          </h1>
          <p className={`text-xl md:text-2xl text-ruby-100 font-light transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            Building worlds, one line of code and one recipe at a time.
          </p>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
        <button
          onClick={scrollToNext}
          className="flex flex-col items-center text-white hover:text-ruby-200 transition-colors duration-300 group"
        >
          <span className="text-sm mb-2 opacity-75">Scroll to explore</span>
          <ChevronDown className="w-6 h-6 animate-bounce-gentle group-hover:scale-110 transition-transform" />
        </button>
      </div>
    </section>
  );
};

export default Hero;