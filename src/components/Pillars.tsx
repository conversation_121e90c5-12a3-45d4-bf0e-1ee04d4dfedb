import React, { useState, useEffect, useRef } from 'react';
import { Code, ChefHat, Gamepad2 } from 'lucide-react';

const Pillars: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredPillar, setHoveredPillar] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const pillars = [
    {
      id: 1,
      title: '...Cool Tech.',
      icon: Code,
      description: 'Sleek, user-friendly mobile and web solutions that just *work*. With two decades of experience, I build digital tools that are intuitive and robust.',
      stat: 'Caffeine Level: 9/10',
      color: 'from-blue-500 to-purple-600',
      bgImage: 'https://images.pexels.com/photos/574071/pexels-photo-574071.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
    },
    {
      id: 2,
      title: '...Delicious Food.',
      icon: ChefHat,
      description: 'From savoury stews to decadent desserts, my kitchen is my lab. I believe good food has the power to make anyone\'s day better.',
      stat: 'Tastiness Score: 11/10 (I\'m biased)',
      color: 'from-orange-500 to-red-600',
      bgImage: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
    },
    {
      id: 3,
      title: '...Fun Games.',
      icon: Gamepad2,
      description: 'As the founder of Viper Den Creations, I build immersive worlds and experiences. It\'s all about creating a little bit of fun and adventure.',
      stat: 'Fun Factor: Maximum',
      color: 'from-green-500 to-teal-600',
      bgImage: 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
    }
  ];

  const scrollToGallery = () => {
    const gallery = document.getElementById('gallery');
    gallery?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="pillars" ref={sectionRef} className="py-20 bg-gradient-to-br from-gray-50 to-ruby-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className={`text-center mb-16 transition-all duration-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-4">
            I like to build<span className="text-ruby-600">...</span>
          </h2>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {pillars.map((pillar, index) => {
            const Icon = pillar.icon;
            return (
              <div
                key={pillar.id}
                className={`relative group cursor-pointer transition-all duration-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                style={{ transitionDelay: `${index * 200}ms` }}
                onMouseEnter={() => setHoveredPillar(pillar.id)}
                onMouseLeave={() => setHoveredPillar(null)}
                onClick={pillar.id === 3 ? scrollToGallery : undefined}
              >
                <div className="relative h-96 rounded-2xl overflow-hidden shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-105">
                  {/* Background Image */}
                  <div 
                    className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-110"
                    style={{ backgroundImage: `url(${pillar.bgImage})` }}
                  />
                  
                  {/* Overlay */}
                  <div className={`absolute inset-0 bg-gradient-to-t ${pillar.color} opacity-80 group-hover:opacity-90 transition-opacity duration-300`} />
                  
                  {/* Content */}
                  <div className="relative z-10 p-8 h-full flex flex-col justify-between text-white">
                    <div className="text-center">
                      <Icon className="w-12 h-12 mx-auto mb-4 animate-float" />
                      <h3 className="text-2xl font-bold mb-4">{pillar.title}</h3>
                    </div>
                    
                    <div>
                      <p className="text-white/90 mb-4 leading-relaxed">{pillar.description}</p>
                      
                      {/* Stat popup */}
                      {hoveredPillar === pillar.id && (
                        <div className="absolute top-4 right-4 bg-black/20 backdrop-blur-sm rounded-lg px-3 py-1 text-sm animate-fade-in">
                          {pillar.stat}
                        </div>
                      )}
                      
                      {/* Enter the Den button for games */}
                      {pillar.id === 3 && hoveredPillar === pillar.id && (
                        <button className="w-full bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg px-4 py-2 text-white font-medium hover:bg-white/30 transition-all duration-200 animate-slide-up">
                          Enter the Den
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Pillars;