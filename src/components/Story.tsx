import React, { useState, useEffect, useRef } from 'react';
import paulImage from '../assets/images/paulvz.png';

const Story: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section id="story" ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-6">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Photo */}
          <div className={`transition-all duration-800 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-8'}`}>
            <div className="relative">
              <img
                src={paulImage}
                alt="Paul"
                className="w-full max-w-md mx-auto rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-gradient-to-br from-ruby-500 to-ruby-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">👋</span>
              </div>
            </div>
          </div>

          {/* Story Text */}
          <div className={`transition-all duration-800 delay-300 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'}`}>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Hello there! I'm Paul.
            </h2>
            
            <div className="prose prose-lg text-gray-700 space-y-6">
              <p>
                For the last 20+ years, my 9-to-5 has been in the world of IT, where I get to be a professional{' '}
                <span className="text-ruby-600 font-medium hover:bg-ruby-50 px-1 rounded transition-colors duration-200">
                  problem-solver
                </span>
                , building slick solutions for web and mobile. It's a world of logic, structure, and the occasional "why isn\'t this working?!" moment that makes the final result so satisfying.
              </p>

              <p>
                But when I'm not wrangling code, my creativity likes to get its hands dirty—literally. I'm a passionate home cook, finding a special kind of zen in turning simple ingredients into something{' '}
                <span className="text-ruby-600 font-medium hover:bg-ruby-50 px-1 rounded transition-colors duration-200">
                  delicious escape
                </span>
                .
              </p>

              <p>
                And then there's the other world I live in—the one I build from scratch. As the creator behind Viper Den Creations, I get to{' '}
                <span className="text-ruby-600 font-medium hover:bg-ruby-50 px-1 rounded transition-colors duration-200">
                  make games
                </span>
                . It's the ultimate playground where my love for tech and storytelling collide.
              </p>

              <p className="text-xl font-medium text-ruby-700">
                So, welcome to my little corner of the internet! It's a bit of everything I love. Take a scroll and see what's cooking.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Story;